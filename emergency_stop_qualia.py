#!/usr/bin/env python3
"""
QUALIA EMERGENCY STOP - Parada de Emergência
Cancela IMEDIATAMENTE todas as execuções QUALIA sem confirmação
"""

import psutil
import time
import os
import sys
from datetime import datetime

def emergency_kill_all():
    """Mata todos os processos QUALIA imediatamente"""
    print("🚨 PARADA DE EMERGÊNCIA QUALIA")
    print("=" * 50)
    
    # Padrões para identificar processos QUALIA
    qualia_patterns = [
        'qualia_supervisor.py',
        'binance_corrected_system.py',
        'qualia_binance_corrected_system.py',
        'qualia_live_trading.py',
        'qualia_adaptive_threshold_system.py',
        'qualia_multi_asset_trading.py',
        'qualia_scalping_system.py',
        'qualia_production_system.py',
        'qualia_monitor.py'
    ]
    
    killed_count = 0
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                # Verificar se é processo Python
                if 'python' not in proc.info['name'].lower():
                    continue
                    
                # Verificar argumentos da linha de comando
                cmdline = proc.info['cmdline']
                if not cmdline:
                    continue
                    
                # Procurar padrões QUALIA nos argumentos
                cmdline_str = ' '.join(cmdline).lower()
                
                for pattern in qualia_patterns:
                    if pattern.lower() in cmdline_str:
                        try:
                            print(f"💀 MATANDO: PID {proc.info['pid']} ({pattern})")
                            proc.kill()  # Força terminação imediata
                            killed_count += 1
                            break
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            pass
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
                
    except Exception as e:
        print(f"❌ Erro: {e}")
        
    print("=" * 50)
    print(f"💀 PROCESSOS ELIMINADOS: {killed_count}")
    print("🚨 PARADA DE EMERGÊNCIA CONCLUÍDA")
    
    return killed_count

if __name__ == "__main__":
    print("🚨 ATENÇÃO: PARADA DE EMERGÊNCIA!")
    print("Este script mata IMEDIATAMENTE todos os processos QUALIA")
    print("Sem confirmação, sem terminação graciosamente!")
    print("")
    
    # Pequena pausa para o usuário ler
    time.sleep(2)
    
    killed = emergency_kill_all()
    
    if killed > 0:
        print(f"\n✅ {killed} processos QUALIA eliminados")
    else:
        print("\n✅ Nenhum processo QUALIA ativo encontrado")
        
    print("\n🏁 Parada de emergência concluída")
