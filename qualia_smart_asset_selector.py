#!/usr/bin/env python3
"""
QUALIA - Seletor Inteligente de Ativos
Sistema que seleciona dinamicamente os melhores ativos para trading

CARACTERÍSTICAS:
✅ Análise de volatilidade em tempo real
✅ Seleção baseada em volume e liquidez
✅ Correlação entre ativos
✅ Oportunidades emergentes
✅ Blacklist automática de ativos problemáticos
"""

import asyncio
import ccxt
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging
import os
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)

class SmartAssetSelector:
    """Seletor inteligente de ativos para trading"""
    
    def __init__(self):
        # Credenciais
        self.api_key = os.getenv('KUCOIN_API_KEY')
        self.api_secret = os.getenv('KUCOIN_API_SECRET')
        self.passphrase = os.getenv('KUCOIN_API_PASSPHRASE')
        
        self.exchange = None
        
        # Universo expandido de ativos
        self.asset_universe = {
            # Tier 1: Máxima liquidez e estabilidade
            "tier1": ["BTC/USDT", "ETH/USDT", "BNB/USDT"],
            
            # Tier 2: Alta liquidez, boa para swing trading
            "tier2": ["ADA/USDT", "SOL/USDT", "XMR/USDT", "LINK/USDT", "DOT/USDT", 
                     "MATIC/USDT", "LTC/USDT", "XRP/USDT"],
            
            # Tier 3: Liquidez média, maior potencial de movimento
            "tier3": ["AVAX/USDT", "ATOM/USDT", "FTM/USDT", "NEAR/USDT", "ALGO/USDT", 
                     "VET/USDT", "SAND/USDT", "MANA/USDT"],
            
            # Tier 4: DeFi e tokens especializados
            "tier4": ["UNI/USDT", "AAVE/USDT", "COMP/USDT", "SUSHI/USDT", "CRV/USDT", 
                     "YFI/USDT", "1INCH/USDT"],
            
            # Tier 5: Altcoins com potencial
            "tier5": ["DOGE/USDT", "SHIB/USDT", "APE/USDT", "GMT/USDT", "STEPN/USDT",
                     "AXS/USDT", "ENJ/USDT"]
        }
        
        # Blacklist dinâmica
        self.blacklisted_assets = set()
        
        # Histórico de performance por ativo
        self.asset_performance = {}
        
    async def initialize(self):
        """Inicializa o seletor"""
        try:
            self.exchange = ccxt.kucoin({
                'apiKey': self.api_key,
                'secret': self.api_secret,
                'password': self.passphrase,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            markets = self.exchange.load_markets()
            logger.info(f"✅ Seletor conectado - {len(markets)} mercados disponíveis")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro inicializando seletor: {e}")
            return False
    
    async def analyze_asset_quality(self, symbol: str) -> Dict:
        """Analisa qualidade de um ativo para trading"""
        try:
            # Obter dados básicos
            ticker = self.exchange.fetch_ticker(symbol)
            
            # Métricas de qualidade
            volume_24h = ticker.get('baseVolume', 0)
            price = ticker.get('last', 0)
            spread = ticker.get('ask', 0) - ticker.get('bid', 0)
            spread_pct = spread / price if price > 0 else 1
            change_24h = ticker.get('percentage', 0) / 100
            
            # Calcular score de qualidade
            volume_score = min(volume_24h / 1000, 10) / 10  # Normalizar volume
            spread_score = max(0, 1 - spread_pct * 1000)    # Penalizar spread alto
            volatility_score = min(abs(change_24h) * 10, 1) # Volatilidade moderada é boa
            
            # Score geral (0-1)
            quality_score = (volume_score * 0.4 + spread_score * 0.4 + volatility_score * 0.2)
            
            return {
                'symbol': symbol,
                'quality_score': quality_score,
                'volume_24h': volume_24h,
                'price': price,
                'spread_pct': spread_pct,
                'change_24h': change_24h,
                'volume_score': volume_score,
                'spread_score': spread_score,
                'volatility_score': volatility_score,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"❌ Erro analisando {symbol}: {e}")
            return None
    
    async def scan_all_assets(self) -> List[Dict]:
        """Escaneia todos os ativos do universo"""
        all_assets = []
        for tier_assets in self.asset_universe.values():
            all_assets.extend(tier_assets)
        
        # Remover duplicatas e blacklisted
        unique_assets = list(set(all_assets) - self.blacklisted_assets)
        
        logger.info(f"🔍 Escaneando {len(unique_assets)} ativos...")
        
        asset_analyses = []
        for symbol in unique_assets:
            analysis = await self.analyze_asset_quality(symbol)
            if analysis:
                asset_analyses.append(analysis)
            
            # Pequena pausa para não sobrecarregar API
            await asyncio.sleep(0.1)
        
        # Ordenar por quality score
        asset_analyses.sort(key=lambda x: x['quality_score'], reverse=True)
        
        return asset_analyses
    
    def select_best_assets(self, asset_analyses: List[Dict], max_assets: int = 10) -> List[str]:
        """Seleciona os melhores ativos baseado na análise"""
        
        if not asset_analyses:
            return []
        
        # Filtros de qualidade mínima
        min_quality_score = 0.3
        min_volume = 100  # Volume mínimo
        max_spread_pct = 0.005  # Spread máximo 0.5%
        
        filtered_assets = []
        for analysis in asset_analyses:
            if (analysis['quality_score'] >= min_quality_score and
                analysis['volume_24h'] >= min_volume and
                analysis['spread_pct'] <= max_spread_pct):
                filtered_assets.append(analysis)
        
        # Diversificação por tier
        selected_assets = []
        assets_by_tier = {tier: [] for tier in self.asset_universe.keys()}
        
        # Classificar por tier
        for analysis in filtered_assets:
            symbol = analysis['symbol']
            for tier, tier_assets in self.asset_universe.items():
                if symbol in tier_assets:
                    assets_by_tier[tier].append(analysis)
                    break
        
        # Selecionar de forma balanceada
        tier_limits = {
            'tier1': min(3, max_assets // 3),      # Até 3 tier1
            'tier2': min(4, max_assets // 2),      # Até 4 tier2
            'tier3': min(2, max_assets // 4),      # Até 2 tier3
            'tier4': min(1, max_assets // 8),      # Até 1 tier4
            'tier5': min(1, max_assets // 10)      # Até 1 tier5
        }
        
        for tier, limit in tier_limits.items():
            tier_assets = assets_by_tier[tier][:limit]
            selected_assets.extend([a['symbol'] for a in tier_assets])
            
            if len(selected_assets) >= max_assets:
                break
        
        # Completar com os melhores restantes se necessário
        if len(selected_assets) < max_assets:
            remaining_symbols = [a['symbol'] for a in filtered_assets 
                               if a['symbol'] not in selected_assets]
            needed = max_assets - len(selected_assets)
            selected_assets.extend(remaining_symbols[:needed])
        
        return selected_assets[:max_assets]
    
    async def get_market_opportunities(self) -> Dict:
        """Identifica oportunidades de mercado em tempo real"""
        
        logger.info("🎯 Identificando oportunidades de mercado...")
        
        # Escanear todos os ativos
        asset_analyses = await self.scan_all_assets()
        
        if not asset_analyses:
            return {'selected_assets': [], 'analysis': []}
        
        # Selecionar os melhores
        selected_assets = self.select_best_assets(asset_analyses, max_assets=12)
        
        # Análise detalhada dos selecionados
        detailed_analysis = [a for a in asset_analyses if a['symbol'] in selected_assets]
        
        # Estatísticas gerais
        avg_quality = np.mean([a['quality_score'] for a in detailed_analysis])
        total_volume = sum([a['volume_24h'] for a in detailed_analysis])
        
        # Identificar oportunidades especiais
        high_volatility = [a for a in detailed_analysis if abs(a['change_24h']) > 0.05]
        high_volume = [a for a in detailed_analysis if a['volume_24h'] > 5000]
        
        opportunities = {
            'timestamp': datetime.now(),
            'selected_assets': selected_assets,
            'total_scanned': len(asset_analyses),
            'total_selected': len(selected_assets),
            'avg_quality_score': avg_quality,
            'total_volume_24h': total_volume,
            'detailed_analysis': detailed_analysis,
            'special_opportunities': {
                'high_volatility': [a['symbol'] for a in high_volatility],
                'high_volume': [a['symbol'] for a in high_volume]
            },
            'blacklisted_count': len(self.blacklisted_assets)
        }
        
        # Log dos resultados
        logger.info(f"✅ Seleção concluída:")
        logger.info(f"   Ativos escaneados: {len(asset_analyses)}")
        logger.info(f"   Ativos selecionados: {len(selected_assets)}")
        logger.info(f"   Quality score médio: {avg_quality:.3f}")
        logger.info(f"   Volume total 24h: {total_volume:,.0f}")
        
        logger.info(f"📊 Ativos selecionados:")
        for i, symbol in enumerate(selected_assets, 1):
            analysis = next(a for a in detailed_analysis if a['symbol'] == symbol)
            logger.info(f"   {i:2d}. {symbol:12s} | Score: {analysis['quality_score']:.3f} | "
                       f"Vol: {analysis['volume_24h']:6.0f} | Change: {analysis['change_24h']:+.2%}")
        
        return opportunities
    
    def update_asset_performance(self, symbol: str, performance_data: Dict):
        """Atualiza performance histórica de um ativo"""
        if symbol not in self.asset_performance:
            self.asset_performance[symbol] = []
        
        self.asset_performance[symbol].append({
            'timestamp': datetime.now(),
            'data': performance_data
        })
        
        # Manter apenas últimos 100 registros
        self.asset_performance[symbol] = self.asset_performance[symbol][-100:]
    
    def add_to_blacklist(self, symbol: str, reason: str):
        """Adiciona ativo à blacklist"""
        self.blacklisted_assets.add(symbol)
        logger.warning(f"⚫ {symbol} adicionado à blacklist: {reason}")
    
    def remove_from_blacklist(self, symbol: str):
        """Remove ativo da blacklist"""
        self.blacklisted_assets.discard(symbol)
        logger.info(f"✅ {symbol} removido da blacklist")

async def main():
    """Teste do seletor inteligente"""
    
    print("🎯 QUALIA - SELETOR INTELIGENTE DE ATIVOS")
    print("=" * 50)
    
    selector = SmartAssetSelector()
    
    try:
        if not await selector.initialize():
            print("❌ Falha na inicialização")
            return
        
        print("🔍 Escaneando mercado para oportunidades...")
        opportunities = await selector.get_market_opportunities()
        
        print("\n📊 OPORTUNIDADES IDENTIFICADAS:")
        print(f"Ativos escaneados: {opportunities['total_scanned']}")
        print(f"Ativos selecionados: {opportunities['total_selected']}")
        print(f"Quality score médio: {opportunities['avg_quality_score']:.3f}")
        
        print("\n🎯 ATIVOS RECOMENDADOS:")
        for i, symbol in enumerate(opportunities['selected_assets'], 1):
            print(f"{i:2d}. {symbol}")
        
        special = opportunities['special_opportunities']
        if special['high_volatility']:
            print(f"\n⚡ Alta volatilidade: {', '.join(special['high_volatility'])}")
        if special['high_volume']:
            print(f"📈 Alto volume: {', '.join(special['high_volume'])}")
        
        return opportunities
        
    except Exception as e:
        logger.error(f"❌ Erro: {e}")

if __name__ == "__main__":
    asyncio.run(main())
